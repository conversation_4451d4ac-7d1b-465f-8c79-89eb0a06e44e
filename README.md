# 七牛云文件上传器

现代化的七牛云文件上传 GUI 工具，基于 CustomTkinter 构建，提供直观的三步上传流程。

## ✨ 特性

- 🎨 **现代化界面**: 基于 CustomTkinter 的美观 GUI
- 📋 **三步上传流程**: 清晰的步骤指示和向导式操作
- 🔐 **灵活认证**: 支持手动输入/粘贴 Authorization Header
- 📊 **实时进度**: 上传进度条和状态显示
- ⚠️ **错误处理**: 完善的错误提示和重试机制
- 📝 **日志记录**: 详细的操作日志和状态信息

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 运行程序

```bash
python -m qiniu_gui.main
```

或者直接运行：

```bash
cd qiniu_gui
python main.py
```

## 📖 使用说明

### 步骤 1: 设置认证
- 输入或粘贴您的 Authorization Header 值
- 通常格式为 `Bearer your-token-here`

### 步骤 2: 选择文件
- 点击"选择文件"按钮选择要上传的文件
- 支持所有文件类型
- 显示文件大小信息

### 步骤 3: 开始上传
- 查看上传进度
- 实时状态更新
- 成功/失败提示

## 🔧 技术架构

### 核心模块

- **main.py**: 主程序入口和 GUI 界面
- **uploader.py**: 七牛云上传核心逻辑
- **widgets.py**: 可复用的 CustomTkinter 组件

### 上传流程

1. **获取 Token**: `GET https://simple.imsummer.cn/api/v3/upload_requests`
2. **获取域名**: `GET https://uc.qiniuapi.com/v4/query?ak={ak}&bucket=static-simple`
3. **上传文件**: `POST https://upload.qiniup.com` (multipart/form-data)

### 技术栈

- **GUI**: CustomTkinter + CTkMessagebox
- **网络**: requests + Session 管理
- **文件处理**: pathlib + uuid
- **日志**: logging 模块
- **类型标注**: Python 3.10+ 完整类型提示

## 🛠️ 开发说明

### 项目结构

```
qiniu_gui/
├── __init__.py      # 包初始化
├── main.py          # 主程序和 GUI
├── uploader.py      # 上传逻辑
└── widgets.py       # UI 组件
requirements.txt     # 依赖列表
README.md           # 说明文档
```

### 代码特点

- ✅ 完整的类型标注
- ✅ 函数命名采用动宾结构
- ✅ 统一的错误处理机制
- ✅ 15秒网络请求超时
- ✅ 线程安全的 GUI 更新
- ✅ 详细的日志记录

## 📋 依赖说明

- `customtkinter>=5.2.0`: 现代化 Tkinter UI 库
- `requests>=2.31.0`: HTTP 请求库
- `CTkMessagebox>=2.5`: CustomTkinter 消息框扩展

## 🎯 使用场景

- 七牛云文件批量上传
- 开发测试文件上传
- 简单的云存储文件管理
- 学习 CustomTkinter GUI 开发

## 🔒 安全说明

- Authorization Header 仅在内存中存储
- 不会保存任何认证信息到本地
- 所有网络请求使用 HTTPS

## 📄 许可证

本项目基于 MIT 许可证开源。

---

**Powered by Claude 4.0 sonnet** 🐾
