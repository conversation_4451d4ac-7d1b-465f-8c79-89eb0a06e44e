@echo off
chcp 65001 >nul
title 七牛云文件上传器

echo.
echo 🚀 七牛云文件上传器
echo 📱 Powered by Claude 4.0 sonnet
echo ========================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到 Python
    echo 💡 请先安装 Python 3.10 或更高版本
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 📦 检查依赖...
python -c "import customtkinter, requests" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动程序
echo ✅ 启动程序...
python run.py

if errorlevel 1 (
    echo.
    echo ❌ 程序运行出错
    pause
)
