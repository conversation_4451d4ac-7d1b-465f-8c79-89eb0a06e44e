"""
七牛云上传 GUI 主程序
分步骤向导式界面，提供现代化的用户体验
"""

import tkinter as tk
from pathlib import Path
from typing import Optional
import customtkinter as ctk
from CTkMessagebox import CTkMessagebox
import threading
import logging

from .uploader import QiniuUploader
from .widgets import (
    StepIndicator, AuthFrame, FileSelectFrame, ProgressFrame, StatusFrame,
    ResultDisplayWindow
)


class QiniuUploaderApp(ctk.CTk):
    """七牛云上传器主应用"""
    
    def __init__(self):
        super().__init__()
        
        # 设置窗口
        self.title("七牛云文件上传器 - Claude 4.0 sonnet")
        self.geometry("700x800")
        self.resizable(True, True)
        
        # 设置主题
        ctk.set_appearance_mode("system")
        ctk.set_default_color_theme("blue")
        
        # 初始化变量
        self.uploader = QiniuUploader()
        self.current_step = 0
        self.upload_token: Optional[str] = None
        self.upload_domains: Optional[list] = None
        
        # 创建界面
        self._create_widgets()
        self._setup_logging()
        
        # 初始状态
        self._update_ui_state()
        
    def _setup_logging(self) -> None:
        """设置日志记录"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
    def _create_widgets(self) -> None:
        """创建主界面组件"""
        # 主标题
        title_label = ctk.CTkLabel(
            self,
            text="🚀 七牛云文件上传器",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 副标题
        subtitle_label = ctk.CTkLabel(
            self,
            text="现代化三步上传流程 - Powered by Claude 4.0 sonnet",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        subtitle_label.pack(pady=(0, 20))
        
        # 步骤指示器
        steps = ["设置认证", "选择文件", "开始上传"]
        self.step_indicator = StepIndicator(self, steps=steps)
        self.step_indicator.pack(pady=20)
        
        # 主内容区域
        self.content_frame = ctk.CTkFrame(self)
        self.content_frame.pack(pady=20, padx=20, fill="both", expand=True)
        
        # 创建各个步骤的框架
        self._create_step_frames()
        
        # 控制按钮区域
        self._create_control_buttons()
        
        # 状态显示区域
        self.status_frame = StatusFrame(self)
        self.status_frame.pack(pady=(10, 20), padx=20, fill="x")
        
    def _create_step_frames(self) -> None:
        """创建各步骤的框架"""
        # 步骤1: Authorization 设置
        self.auth_frame = AuthFrame(self.content_frame)
        
        # 步骤2: 文件选择
        self.file_frame = FileSelectFrame(self.content_frame)
        self.file_frame.set_file_callback(self._on_file_selected)
        
        # 步骤3: 进度显示
        self.progress_frame = ProgressFrame(self.content_frame)
        
        # 初始显示第一个步骤
        self.auth_frame.pack(fill="both", expand=True)
        
    def _create_control_buttons(self) -> None:
        """创建控制按钮"""
        button_frame = ctk.CTkFrame(self)
        button_frame.pack(pady=10, padx=20, fill="x")
        
        # 上一步按钮
        self.prev_button = ctk.CTkButton(
            button_frame,
            text="⬅️ 上一步",
            command=self._prev_step,
            width=120,
            state="disabled"
        )
        self.prev_button.pack(side="left", padx=10)
        
        # 下一步/上传按钮
        self.next_button = ctk.CTkButton(
            button_frame,
            text="下一步 ➡️",
            command=self._next_step,
            width=120
        )
        self.next_button.pack(side="right", padx=10)
        
        # SSL验证开关
        self.ssl_switch = ctk.CTkSwitch(
            button_frame,
            text="SSL验证",
            command=self._toggle_ssl_verify
        )
        self.ssl_switch.pack(side="right", padx=10)
        # self.ssl_switch.select()  # 默认禁用SSL验证以解决证书问题

        # 重置按钮
        self.reset_button = ctk.CTkButton(
            button_frame,
            text="🔄 重置",
            command=self._reset_app,
            width=100,
            fg_color="orange",
            hover_color="darkorange"
        )
        self.reset_button.pack(side="right", padx=(0, 10))
        
    def _update_ui_state(self) -> None:
        """更新界面状态"""
        # 更新步骤指示器
        self.step_indicator.update_step(self.current_step)
        
        # 隐藏所有步骤框架
        for frame in [self.auth_frame, self.file_frame, self.progress_frame]:
            frame.pack_forget()
            
        # 显示当前步骤框架
        if self.current_step == 0:
            self.auth_frame.pack(fill="both", expand=True)
            self.prev_button.configure(state="disabled")
            self.next_button.configure(text="下一步 ➡️")
            
        elif self.current_step == 1:
            self.file_frame.pack(fill="both", expand=True)
            self.prev_button.configure(state="normal")
            self.next_button.configure(text="下一步 ➡️")
            
        elif self.current_step == 2:
            self.progress_frame.pack(fill="both", expand=True)
            self.prev_button.configure(state="normal")
            self.next_button.configure(text="🚀 开始上传")
            
    def _prev_step(self) -> None:
        """上一步"""
        if self.current_step > 0:
            self.current_step -= 1
            self._update_ui_state()
            self.status_frame.add_status(f"返回到步骤 {self.current_step + 1}")
            
    def _next_step(self) -> None:
        """下一步或开始上传"""
        if self.current_step == 0:
            # 验证 Authorization
            auth = self.auth_frame.get_authorization()
            if not auth:
                CTkMessagebox(
                    title="错误",
                    message="请输入 Authorization Header",
                    icon="cancel"
                )
                return
                
            self.uploader.set_authorization(auth)
            self.status_frame.add_status("Authorization 已设置", "success")
            self.current_step = 1
            
        elif self.current_step == 1:
            # 验证文件选择
            if not self.file_frame.get_selected_file():
                CTkMessagebox(
                    title="错误", 
                    message="请选择要上传的文件",
                    icon="cancel"
                )
                return
                
            self.status_frame.add_status("文件已选择", "success")
            self.current_step = 2
            
        elif self.current_step == 2:
            # 开始上传流程
            self._start_upload()
            return
            
        self._update_ui_state()
        
    def _on_file_selected(self, file_path: Path) -> None:
        """文件选择回调"""
        self.status_frame.add_status(f"已选择文件: {file_path.name}", "info")

    def _toggle_ssl_verify(self) -> None:
        """切换SSL验证状态"""
        ssl_enabled = self.ssl_switch.get()
        self.uploader.set_ssl_verify(ssl_enabled)
        status = "启用" if ssl_enabled else "禁用"
        self.status_frame.add_status(f"SSL验证已{status}", "info")

        if not ssl_enabled:
            self.status_frame.add_status("⚠️ 警告: SSL验证已禁用，连接安全性降低", "warning")
        
    def _start_upload(self) -> None:
        """开始上传流程"""
        # 禁用按钮防止重复操作
        self.next_button.configure(state="disabled", text="上传中...")
        self.prev_button.configure(state="disabled")
        
        # 重置进度
        self.progress_frame.reset()
        self.status_frame.add_status("开始上传流程...", "info")
        
        # 在新线程中执行上传
        upload_thread = threading.Thread(target=self._upload_worker)
        upload_thread.daemon = True
        upload_thread.start()
        
    def _upload_worker(self) -> None:
        """上传工作线程"""
        try:
            print("\n" + "="*50)
            print("🚀 开始上传流程")
            print("="*50)

            # 第一步：获取 token
            print("\n[STEP 1] 获取上传 token")
            self.progress_frame.set_status("正在获取上传 token...", "blue")
            success, message, token = self.uploader.get_upload_token()

            if not success:
                print(f"[STEP 1 FAILED] {message}")
                self._upload_error(f"获取 token 失败: {message}")
                return

            self.upload_token = token
            print(f"[STEP 1 SUCCESS] 成功获取上传 token")
            self.status_frame.add_status("✅ 成功获取上传 token", "success")
            
            # 第二步：获取上传域名
            print("\n[STEP 2] 获取上传域名")
            self.progress_frame.set_status("正在获取上传域名...", "blue")
            success, message, domains = self.uploader.get_upload_domains(token)

            if not success:
                print(f"[STEP 2 FAILED] {message}")
                self._upload_error(f"获取上传域名失败: {message}")
                return

            self.upload_domains = domains
            print(f"[STEP 2 SUCCESS] 成功获取 {len(domains)} 个上传域名: {domains}")
            self.status_frame.add_status(f"✅ 成功获取 {len(domains)} 个上传域名", "success")
            
            # 第三步：上传文件
            print("\n[STEP 3] 上传文件")
            file_path = self.file_frame.get_selected_file()
            if not file_path:
                print("[STEP 3 FAILED] 未选择文件")
                self._upload_error("未选择文件")
                return

            print(f"[DEBUG] 上传文件: {file_path}")
            print(f"[DEBUG] 文件大小: {file_path.stat().st_size / (1024*1024):.2f} MB")

            self.progress_frame.set_status("正在上传文件...", "blue")

            def progress_callback(current: int, total: int):
                # 在主线程中更新进度
                self.after(0, lambda: self.progress_frame.update_progress(current, total))
                print(f"[PROGRESS] {current}/{total} bytes ({current/total*100:.1f}%)")

            success, message, result = self.uploader.upload_file(
                file_path, token, progress_callback
            )

            if success:
                print(f"[STEP 3 SUCCESS] 上传完成: {message}")
                print(f"[DEBUG] 上传结果: {result}")
                self._upload_success(message, result)
            else:
                print(f"[STEP 3 FAILED] {message}")
                self._upload_error(f"上传失败: {message}")

        except Exception as e:
            error_msg = f"上传过程中发生异常: {str(e)}"
            print(f"[EXCEPTION] {error_msg}")
            print(f"[DEBUG] 异常类型: {type(e).__name__}")
            import traceback
            print(f"[TRACEBACK] {traceback.format_exc()}")
            self._upload_error(error_msg)
            
    def _upload_success(self, message: str, result: dict) -> None:
        """上传成功处理"""
        self.after(0, lambda: self._handle_upload_success(message, result))
        
    def _handle_upload_success(self, message: str, result: dict) -> None:
        """在主线程中处理上传成功"""
        self.progress_frame.set_status("✅ 上传完成!", "green")
        self.status_frame.add_status(message, "success")

        # 提取访问链接
        file_url = None
        full_data = None

        if result:
            if 'url' in result:
                file_url = result['url']
                full_data = result.get('full_data', result)
            elif 'file_url' in result:
                file_url = result['file_url']
                full_data = result
            else:
                full_data = result

        # 显示访问链接（最重要的信息）
        if file_url:
            self.status_frame.add_status("🔗 文件访问链接:", "success")
            self.status_frame.add_status(f"   {file_url}", "success")

            # 复制链接到剪贴板
            try:
                self.clipboard_clear()
                self.clipboard_append(file_url)
                self.status_frame.add_status("📋 链接已自动复制到剪贴板", "info")
            except Exception:
                pass

        # 创建简洁的成功消息，重点突出访问链接
        if file_url:
            success_message = f"🎉 文件上传成功！\n\n🔗 访问链接:\n{file_url}\n\n📋 链接已复制到剪贴板"
        else:
            success_message = f"🎉 {message}"

        # 显示详细结果窗口（如果有完整数据）
        if full_data and len(full_data) > 1:
            result_window = ResultDisplayWindow(
                self,
                "上传成功 - 详细结果",
                full_data
            )

        # 显示成功消息，重点突出访问链接
        CTkMessagebox(
            title="🎉 上传成功",
            message=success_message,
            icon="check"
        )

        # 恢复按钮状态
        self.next_button.configure(state="normal", text="🚀 开始上传")
        self.prev_button.configure(state="normal")
        
    def _upload_error(self, error_message: str) -> None:
        """上传错误处理"""
        self.after(0, lambda: self._handle_upload_error(error_message))
        
    def _handle_upload_error(self, error_message: str) -> None:
        """在主线程中处理上传错误"""
        self.progress_frame.set_status("❌ 上传失败", "red")
        self.status_frame.add_status(error_message, "error")
        
        # 显示错误消息
        CTkMessagebox(
            title="上传失败",
            message=error_message,
            icon="cancel"
        )
        
        # 恢复按钮状态
        self.next_button.configure(state="normal", text="🚀 重试上传")
        self.prev_button.configure(state="normal")
        
    def _reset_app(self) -> None:
        """重置应用状态"""
        # 确认重置
        msg = CTkMessagebox(
            title="确认重置",
            message="确定要重置所有设置吗？",
            icon="question",
            option_1="取消",
            option_2="确定"
        )
        
        if msg.get() == "确定":
            self.current_step = 0
            self.upload_token = None
            self.upload_domains = None
            
            # 清空输入
            self.auth_frame.set_authorization("")
            self.progress_frame.reset()
            self.status_frame.clear_status()
            
            # 更新界面
            self._update_ui_state()
            self.status_frame.add_status("应用已重置", "info")


def main():
    """主函数"""
    # 设置高DPI支持
    try:
        from ctypes import windll
        windll.shcore.SetProcessDpiAwareness(1)
    except:
        pass
        
    app = QiniuUploaderApp()
    app.mainloop()


if __name__ == "__main__":
    main()
