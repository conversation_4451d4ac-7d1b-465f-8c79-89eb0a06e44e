#!/usr/bin/env python3
"""
七牛云上传器启动脚本
快速启动程序的便捷入口
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from qiniu_gui.main import main
    
    if __name__ == "__main__":
        print("🚀 启动七牛云文件上传器...")
        print("📱 Powered by Claude 4.0 sonnet")
        print("-" * 40)
        main()
        
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("💡 请确保已安装所有依赖:")
    print("   pip install -r requirements.txt")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ 启动失败: {e}")
    sys.exit(1)
