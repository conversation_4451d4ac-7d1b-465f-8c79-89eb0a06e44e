"""
七牛云上传核心逻辑模块
实现三步上传流程：获取token -> 获取域名 -> 上传文件
"""

import logging
import uuid
import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry


class QiniuUploader:
    """七牛云文件上传器"""
    
    def __init__(self, authorization: str = "", timeout: int = 15):
        """
        初始化上传器
        
        Args:
            authorization: Authorization header 值
            timeout: 请求超时时间（秒）
        """
        self.authorization = authorization
        self.timeout = timeout
        self.logger = self._setup_logger()
        self.session = self._setup_session()
        
        # API 端点
        self.token_url = "https://simple.imsummer.cn/api/v3/upload_requests"
        self.domain_url_template = "https://uc.qiniuapi.com/v4/query?ak={ak}&bucket=static-simple"
        self.upload_url = "https://upload.qiniup.com"
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("QiniuUploader")
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
        
    def _setup_session(self) -> requests.Session:
        """设置请求会话，包含重试策略"""
        session = requests.Session()

        # 设置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)

        # 设置SSL验证（可选择禁用以解决证书问题）
        session.verify = False  # 临时禁用SSL验证以解决证书问题
        print("[WARNING] SSL验证已禁用，这可能降低连接安全性")

        # 设置User-Agent
        session.headers.update({
            'User-Agent': 'QiniuUploader/1.0.0 (Claude 4.0 sonnet)'
        })

        return session
        
    def set_authorization(self, authorization: str) -> None:
        """设置 Authorization header"""
        self.authorization = authorization
        self.logger.info("Authorization header 已更新")

    def set_ssl_verify(self, verify: bool) -> None:
        """设置SSL验证选项"""
        self.session.verify = verify
        self.logger.info(f"SSL验证已{'启用' if verify else '禁用'}")
        print(f"[CONFIG] SSL验证: {'启用' if verify else '禁用'}")
        
    def get_upload_token(self) -> Tuple[bool, str, Optional[str]]:
        """
        第一步：获取上传 token

        Returns:
            Tuple[成功标志, 消息, token]
        """
        if not self.authorization:
            return False, "请先设置 Authorization header", None

        headers = {"Authorization": self.authorization}

        try:
            self.logger.info("开始获取上传 token...")
            self.logger.info(f"请求URL: {self.token_url}")
            self.logger.info(f"请求头: Authorization: {self.authorization[:20]}...")

            print(f"[DEBUG] 正在请求: {self.token_url}")
            print(f"[DEBUG] 超时设置: {self.timeout}秒")
            print(f"[DEBUG] SSL验证: {'启用' if self.session.verify else '禁用'}")

            response = self.session.get(
                self.token_url,
                headers=headers,
                timeout=self.timeout
            )

            print(f"[DEBUG] 响应状态码: {response.status_code}")
            print(f"[DEBUG] 响应头: {dict(response.headers)}")

            response.raise_for_status()

            data = response.json()
            print(f"[DEBUG] 响应数据: {data}")

            token = data.get("token")

            if not token:
                return False, "响应中未找到 token 字段", None

            self.logger.info("成功获取上传 token")
            print(f"[DEBUG] 获取到token: {token[:50]}...")
            return True, "成功获取上传 token", token
            
        except requests.exceptions.Timeout:
            error_msg = "请求超时，请检查网络连接"
            self.logger.error(error_msg)
            print(f"[ERROR] {error_msg}")
            return False, error_msg, None

        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 错误: {e.response.status_code}"
            if hasattr(e.response, 'text'):
                error_msg += f", 响应内容: {e.response.text[:200]}"
            self.logger.error(error_msg)
            print(f"[ERROR] {error_msg}")
            return False, error_msg, None

        except requests.exceptions.SSLError as e:
            error_msg = f"SSL证书验证失败: {str(e)}"
            self.logger.error(error_msg)
            print(f"[ERROR] {error_msg}")
            print("[SOLUTION] 可能的解决方案:")
            print("  1. 检查系统时间是否正确")
            print("  2. 更新CA证书包")
            print("  3. 临时禁用SSL验证（不推荐）")
            return False, error_msg, None

        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            print(f"[ERROR] {error_msg}")
            print(f"[DEBUG] 异常类型: {type(e).__name__}")
            return False, error_msg, None

        except (KeyError, ValueError) as e:
            error_msg = f"响应数据解析失败: {str(e)}"
            self.logger.error(error_msg)
            print(f"[ERROR] {error_msg}")
            return False, error_msg, None
            
    def get_upload_domains(self, token: str) -> Tuple[bool, str, Optional[List[str]]]:
        """
        第二步：获取上传域名列表
        
        Args:
            token: 第一步获取的 token
            
        Returns:
            Tuple[成功标志, 消息, 域名列表]
        """
        try:
            # 解析 token 获取 ak
            token_parts = token.split(":")
            if len(token_parts) < 1:
                return False, "token 格式错误", None
                
            ak = token_parts[0]
            domain_url = self.domain_url_template.format(ak=ak)
            
            self.logger.info(f"开始获取上传域名，ak: {ak}")
            response = self.session.get(domain_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            hosts = data.get("hosts", [])
            
            if not hosts:
                return False, "未获取到可用的上传域名", None
                
            self.logger.info(f"成功获取 {len(hosts)} 个上传域名")
            return True, f"成功获取 {len(hosts)} 个上传域名", hosts
            
        except requests.exceptions.Timeout:
            error_msg = "请求超时，请检查网络连接"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"HTTP 错误: {e.response.status_code}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except (KeyError, ValueError) as e:
            error_msg = f"响应数据解析失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
    def generate_file_key(self, file_path: Path) -> str:
        """生成随机文件 key"""
        file_extension = file_path.suffix
        random_key = str(uuid.uuid4())
        return f"{random_key}{file_extension}"
        
    def upload_file(
        self, 
        file_path: Path, 
        token: str, 
        progress_callback: Optional[callable] = None
    ) -> Tuple[bool, str, Optional[Dict[str, Any]]]:
        """
        第三步：上传文件
        
        Args:
            file_path: 要上传的文件路径
            token: 上传 token
            progress_callback: 进度回调函数，接收 (已上传字节, 总字节) 参数
            
        Returns:
            Tuple[成功标志, 消息, 响应数据]
        """
        if not file_path.exists():
            return False, "文件不存在", None
            
        try:
            # 生成随机 key
            file_key = self.generate_file_key(file_path)
            
            # 准备表单数据
            files = {
                'file': (file_path.name, open(file_path, 'rb'), 'application/octet-stream')
            }
            data = {
                'token': token,
                'key': file_key
            }
            
            self.logger.info(f"开始上传文件: {file_path.name}, key: {file_key}")
            
            # 如果有进度回调，使用流式上传
            if progress_callback:
                response = self._upload_with_progress(files, data, progress_callback)
            else:
                response = self.session.post(
                    self.upload_url,
                    files=files,
                    data=data,
                    timeout=self.timeout * 3  # 上传超时时间更长
                )
                
            response.raise_for_status()
            
            # 关闭文件
            files['file'][1].close()

            # 解析响应数据
            try:
                result_data = response.json() if response.content else {}
                print(f"[DEBUG] 完整上传响应: {result_data}")
            except ValueError as e:
                print(f"[WARNING] 响应不是有效JSON: {e}")
                result_data = {
                    "raw_response": response.text,
                    "status_code": response.status_code,
                    "headers": dict(response.headers)
                }

            # 生成完整的访问URL
            file_url = None
            if 'key' in result_data:
                file_url = f"https://static-simple.imsummer.cn/{result_data['key']}"
                print(f"[SUCCESS] 文件访问链接: {file_url}")

            # 添加额外的元数据
            enhanced_data = {
                "hash": result_data.get("hash"),
                "key": result_data.get("key"),
                "file_url": file_url,  # 完整的访问链接
                "upload_time": str(datetime.datetime.now()),
                "file_name": file_path.name,
                "file_size": file_path.stat().st_size,
                "upload_url": self.upload_url
            }

            self.logger.info("文件上传成功")
            print(f"[SUCCESS] 上传完成，增强数据: {enhanced_data}")

            # 最终只返回访问链接作为主要结果
            if file_url:
                return True, f"文件上传成功", {"url": file_url, "full_data": enhanced_data}
            else:
                return True, "文件上传成功", enhanced_data
            
        except requests.exceptions.Timeout:
            error_msg = "上传超时，请检查网络连接或文件大小"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except requests.exceptions.HTTPError as e:
            error_msg = f"上传失败，HTTP 错误: {e.response.status_code}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求失败: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
        except Exception as e:
            error_msg = f"上传过程中发生错误: {str(e)}"
            self.logger.error(error_msg)
            return False, error_msg, None
            
    def _upload_with_progress(
        self, 
        files: Dict, 
        data: Dict, 
        progress_callback: callable
    ) -> requests.Response:
        """带进度回调的文件上传"""
        # 这里简化实现，实际项目中可以使用 requests-toolbelt 的 MultipartEncoder
        # 来实现真正的进度追踪
        file_obj = files['file'][1]
        file_size = file_obj.seek(0, 2)  # 获取文件大小
        file_obj.seek(0)  # 重置文件指针
        
        # 模拟进度回调（简化版本）
        if progress_callback:
            progress_callback(0, file_size)
            
        response = self.session.post(
            self.upload_url,
            files=files,
            data=data,
            timeout=self.timeout * 3
        )
        
        # 上传完成后调用进度回调
        if progress_callback:
            progress_callback(file_size, file_size)
            
        return response
