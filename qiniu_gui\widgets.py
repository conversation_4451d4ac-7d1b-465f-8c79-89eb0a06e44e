"""
可复用的 CustomTkinter 组件模块
包含步骤指示器、输入框架、文件选择框架等组件
"""

import tkinter as tk
from pathlib import Path
from typing import List, Optional, Callable, Any
import customtkinter as ctk
from tkinter import filedialog


class StepIndicator(ctk.CTkFrame):
    """步骤指示器组件"""
    
    def __init__(
        self, 
        master: Any, 
        steps: List[str], 
        current_step: int = 0,
        **kwargs
    ):
        super().__init__(master, **kwargs)
        
        self.steps = steps
        self.current_step = current_step
        self.step_labels: List[ctk.CTkLabel] = []
        self.step_circles: List[ctk.CTkFrame] = []
        
        self._create_widgets()
        self.update_step(current_step)
        
    def _create_widgets(self) -> None:
        """创建步骤指示器组件"""
        for i, step_text in enumerate(self.steps):
            # 创建步骤圆圈
            circle_frame = ctk.CTkFrame(
                self, 
                width=30, 
                height=30, 
                corner_radius=15,
                fg_color="gray"
            )
            circle_frame.grid(row=0, column=i*2, padx=5, pady=10)
            
            # 步骤编号
            step_number = ctk.CTkLabel(
                circle_frame,
                text=str(i+1),
                font=ctk.CTkFont(size=12, weight="bold"),
                text_color="white"
            )
            step_number.place(relx=0.5, rely=0.5, anchor="center")
            
            # 步骤文本
            step_label = ctk.CTkLabel(
                self,
                text=step_text,
                font=ctk.CTkFont(size=11)
            )
            step_label.grid(row=1, column=i*2, padx=5, pady=(0, 10))
            
            self.step_circles.append(circle_frame)
            self.step_labels.append(step_label)
            
            # 添加连接线（除了最后一个步骤）
            if i < len(self.steps) - 1:
                line = ctk.CTkFrame(self, height=2, fg_color="gray")
                line.grid(row=0, column=i*2+1, sticky="ew", padx=2, pady=20)
                
    def update_step(self, step: int) -> None:
        """更新当前步骤"""
        self.current_step = step
        
        for i, (circle, label) in enumerate(zip(self.step_circles, self.step_labels)):
            if i < step:
                # 已完成的步骤
                circle.configure(fg_color="green")
                label.configure(text_color="green")
            elif i == step:
                # 当前步骤
                circle.configure(fg_color="blue")
                label.configure(text_color="blue")
            else:
                # 未开始的步骤
                circle.configure(fg_color="gray")
                label.configure(text_color="gray")


class AuthFrame(ctk.CTkFrame):
    """Authorization 输入框架"""
    
    def __init__(self, master: Any, **kwargs):
        super().__init__(master, **kwargs)
        
        self.auth_var = tk.StringVar()
        self._create_widgets()
        
    def _create_widgets(self) -> None:
        """创建认证输入组件"""
        # 标题
        title_label = ctk.CTkLabel(
            self,
            text="步骤 1: 设置 Authorization",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 说明文本
        desc_label = ctk.CTkLabel(
            self,
            text="请输入或粘贴您的 Authorization Header 值",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        desc_label.pack(pady=(0, 15))
        
        # 输入框
        self.auth_entry = ctk.CTkEntry(
            self,
            textvariable=self.auth_var,
            placeholder_text="Bearer your-token-here 或其他格式",
            width=400,
            height=35
        )
        self.auth_entry.pack(pady=10)
        
        # 提示信息
        hint_label = ctk.CTkLabel(
            self,
            text="💡 提示：通常以 'Bearer ' 开头",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        hint_label.pack(pady=(5, 20))
        
    def get_authorization(self) -> str:
        """获取 Authorization 值"""
        return self.auth_var.get().strip()
        
    def set_authorization(self, auth: str) -> None:
        """设置 Authorization 值"""
        self.auth_var.set(auth)


class FileSelectFrame(ctk.CTkFrame):
    """文件选择框架"""
    
    def __init__(self, master: Any, **kwargs):
        super().__init__(master, **kwargs)
        
        self.selected_file: Optional[Path] = None
        self.file_callback: Optional[Callable[[Path], None]] = None
        self._create_widgets()
        
    def _create_widgets(self) -> None:
        """创建文件选择组件"""
        # 标题
        title_label = ctk.CTkLabel(
            self,
            text="步骤 2: 选择要上传的文件",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 文件选择按钮
        self.select_button = ctk.CTkButton(
            self,
            text="📁 选择文件",
            command=self._select_file,
            width=200,
            height=40,
            font=ctk.CTkFont(size=14)
        )
        self.select_button.pack(pady=15)
        
        # 选中文件显示
        self.file_label = ctk.CTkLabel(
            self,
            text="未选择文件",
            font=ctk.CTkFont(size=12),
            text_color="gray"
        )
        self.file_label.pack(pady=10)
        
        # 文件信息显示
        self.info_label = ctk.CTkLabel(
            self,
            text="",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        self.info_label.pack(pady=(0, 20))
        
    def _select_file(self) -> None:
        """选择文件"""
        file_path = filedialog.askopenfilename(
            title="选择要上传的文件",
            filetypes=[
                ("所有文件", "*.*"),
                ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("文档文件", "*.pdf *.doc *.docx *.txt"),
                ("压缩文件", "*.zip *.rar *.7z")
            ]
        )
        
        if file_path:
            self.selected_file = Path(file_path)
            self._update_file_display()
            
            if self.file_callback:
                self.file_callback(self.selected_file)
                
    def _update_file_display(self) -> None:
        """更新文件显示信息"""
        if self.selected_file:
            # 显示文件名
            self.file_label.configure(
                text=f"✅ {self.selected_file.name}",
                text_color="green"
            )
            
            # 显示文件大小
            try:
                file_size = self.selected_file.stat().st_size
                size_mb = file_size / (1024 * 1024)
                if size_mb < 1:
                    size_text = f"{file_size / 1024:.1f} KB"
                else:
                    size_text = f"{size_mb:.1f} MB"
                    
                self.info_label.configure(text=f"文件大小: {size_text}")
            except Exception:
                self.info_label.configure(text="无法获取文件信息")
        else:
            self.file_label.configure(text="未选择文件", text_color="gray")
            self.info_label.configure(text="")
            
    def get_selected_file(self) -> Optional[Path]:
        """获取选中的文件"""
        return self.selected_file
        
    def set_file_callback(self, callback: Callable[[Path], None]) -> None:
        """设置文件选择回调"""
        self.file_callback = callback


class ProgressFrame(ctk.CTkFrame):
    """进度显示框架"""
    
    def __init__(self, master: Any, **kwargs):
        super().__init__(master, **kwargs)
        
        self.progress_var = tk.DoubleVar()
        self._create_widgets()
        
    def _create_widgets(self) -> None:
        """创建进度显示组件"""
        # 标题
        title_label = ctk.CTkLabel(
            self,
            text="步骤 3: 上传进度",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(20, 10))
        
        # 进度条
        self.progress_bar = ctk.CTkProgressBar(
            self,
            width=400,
            height=20,
            variable=self.progress_var
        )
        self.progress_bar.pack(pady=15)
        
        # 进度文本
        self.progress_label = ctk.CTkLabel(
            self,
            text="准备上传...",
            font=ctk.CTkFont(size=12)
        )
        self.progress_label.pack(pady=10)
        
        # 状态信息
        self.status_label = ctk.CTkLabel(
            self,
            text="",
            font=ctk.CTkFont(size=10),
            text_color="gray"
        )
        self.status_label.pack(pady=(0, 20))
        
    def update_progress(self, current: int, total: int) -> None:
        """更新进度"""
        if total > 0:
            progress = current / total
            self.progress_var.set(progress)
            
            # 更新进度文本
            percent = int(progress * 100)
            self.progress_label.configure(text=f"上传进度: {percent}%")
            
            # 更新状态信息
            current_mb = current / (1024 * 1024)
            total_mb = total / (1024 * 1024)
            self.status_label.configure(
                text=f"{current_mb:.1f} MB / {total_mb:.1f} MB"
            )
        else:
            self.progress_var.set(0)
            self.progress_label.configure(text="准备上传...")
            self.status_label.configure(text="")
            
    def set_status(self, status: str, color: str = "gray") -> None:
        """设置状态信息"""
        self.progress_label.configure(text=status, text_color=color)
        
    def reset(self) -> None:
        """重置进度"""
        self.progress_var.set(0)
        self.progress_label.configure(text="准备上传...", text_color="gray")
        self.status_label.configure(text="")


class StatusFrame(ctk.CTkFrame):
    """状态信息显示框架"""
    
    def __init__(self, master: Any, **kwargs):
        super().__init__(master, **kwargs)
        
        self._create_widgets()
        
    def _create_widgets(self) -> None:
        """创建状态显示组件"""
        # 状态文本框（只读）
        self.status_text = ctk.CTkTextbox(
            self,
            width=500,
            height=120,
            font=ctk.CTkFont(size=11)
        )
        self.status_text.pack(pady=10, padx=20, fill="both", expand=True)
        
        # 清空按钮
        clear_button = ctk.CTkButton(
            self,
            text="清空日志",
            command=self.clear_status,
            width=100,
            height=30
        )
        clear_button.pack(pady=(0, 10))
        
    def add_status(self, message: str, level: str = "info") -> None:
        """添加状态信息"""
        import datetime
        
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        
        # 根据级别设置前缀
        prefix_map = {
            "info": "ℹ️",
            "success": "✅", 
            "warning": "⚠️",
            "error": "❌"
        }
        prefix = prefix_map.get(level, "ℹ️")
        
        formatted_message = f"[{timestamp}] {prefix} {message}\n"
        
        # 添加到文本框
        self.status_text.insert("end", formatted_message)
        self.status_text.see("end")  # 滚动到底部
        
    def clear_status(self) -> None:
        """清空状态信息"""
        self.status_text.delete("1.0", "end")
